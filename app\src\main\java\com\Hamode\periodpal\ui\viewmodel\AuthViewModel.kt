package com.Hamode.periodpal.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.Hamode.periodpal.data.models.UserProfile
import com.Hamode.periodpal.data.repository.FirebaseRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.time.LocalDate

class AuthViewModel(
    private val repository: FirebaseRepository = FirebaseRepository(
        FirebaseAuth.getInstance(),
        FirebaseFirestore.getInstance()
    )
) : ViewModel() {

    private val _authState = MutableStateFlow<AuthState>(AuthState.Loading)
    val authState: StateFlow<AuthState> = _authState.asStateFlow()

    private val _errorMessage = MutableStateFlow("")
    val errorMessage: StateFlow<String> = _errorMessage.asStateFlow()

    init {
        checkAuthState()
    }

    private fun checkAuthState() {
        if (repository.isUserLoggedIn()) {
            _authState.value = AuthState.Authenticated
        } else {
            _authState.value = AuthState.Unauthenticated
        }
    }

    fun signIn(email: String, password: String) {
        if (email.isBlank() || password.isBlank()) {
            _errorMessage.value = "Please fill in all fields"
            return
        }

        _authState.value = AuthState.Loading
        viewModelScope.launch {
            repository.signInWithEmail(email, password)
                .onSuccess {
                    _authState.value = AuthState.Authenticated
                    _errorMessage.value = ""
                }
                .onFailure { exception ->
                    _authState.value = AuthState.Unauthenticated
                    _errorMessage.value = getErrorMessage(exception)
                }
        }
    }

    fun signUp(email: String, password: String, displayName: String) {
        if (email.isBlank() || password.isBlank() || displayName.isBlank()) {
            _errorMessage.value = "Please fill in all fields"
            return
        }

        if (password.length < 6) {
            _errorMessage.value = "Password must be at least 6 characters"
            return
        }

        _authState.value = AuthState.Loading
        viewModelScope.launch {
            repository.signUpWithEmail(email, password)
                .onSuccess { user ->
                    // Create user profile
                    val profile = UserProfile(
                        userId = user.uid,
                        email = email,
                        displayName = displayName,
                        createdAt = LocalDate.now(),
                        updatedAt = LocalDate.now()
                    )
                    
                    repository.saveUserProfile(profile)
                        .onSuccess {
                            _authState.value = AuthState.Authenticated
                            _errorMessage.value = ""
                        }
                        .onFailure { exception ->
                            _authState.value = AuthState.Unauthenticated
                            _errorMessage.value = "Failed to create profile: ${exception.message}"
                        }
                }
                .onFailure { exception ->
                    _authState.value = AuthState.Unauthenticated
                    _errorMessage.value = getErrorMessage(exception)
                }
        }
    }

    fun signOut() {
        repository.signOut()
        _authState.value = AuthState.Unauthenticated
        _errorMessage.value = ""
    }

    fun clearError() {
        _errorMessage.value = ""
    }

    private fun getErrorMessage(exception: Throwable): String {
        return when {
            exception.message?.contains("network") == true -> "Network error. Please check your connection."
            exception.message?.contains("password") == true -> "Invalid email or password."
            exception.message?.contains("email") == true -> "Invalid email format."
            exception.message?.contains("user-not-found") == true -> "No account found with this email."
            exception.message?.contains("wrong-password") == true -> "Incorrect password."
            exception.message?.contains("email-already-in-use") == true -> "An account with this email already exists."
            exception.message?.contains("weak-password") == true -> "Password is too weak."
            else -> exception.message ?: "An unknown error occurred."
        }
    }
}

sealed class AuthState {
    object Loading : AuthState()
    object Authenticated : AuthState()
    object Unauthenticated : AuthState()
}
