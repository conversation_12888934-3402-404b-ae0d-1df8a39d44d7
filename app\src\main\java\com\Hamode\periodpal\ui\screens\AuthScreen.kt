package com.Hamode.periodpal.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.Hamode.periodpal.ui.theme.*
import com.Hamode.periodpal.ui.viewmodel.AuthViewModel
import androidx.lifecycle.viewmodel.compose.viewModel
import kotlinx.coroutines.launch

@Composable
fun AuthScreen(
    onLoginSuccess: () -> Unit,
    onSignUpSuccess: () -> Unit,
    modifier: Modifier = Modifier,
    authViewModel: AuthViewModel = viewModel()
) {
    var isLoginMode by remember { mutableStateOf(true) }
    var email by remember { mutableStateOf("") }
    var password by remember { mutableStateOf("") }
    var confirmPassword by remember { mutableStateOf("") }
    var displayName by remember { mutableStateOf("") }
    var isPasswordVisible by remember { mutableStateOf(false) }
    var isLoading by remember { mutableStateOf(false) }
    var errorMessage by remember { mutableStateOf("") }

    val coroutineScope = rememberCoroutineScope()

    // Observe auth error messages
    val authError by authViewModel.errorMessage.collectAsState()

    Column(
        modifier = modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        RosePink40.copy(alpha = 0.1f),
                        Color.White,
                        SoftPink40.copy(alpha = 0.2f)
                    )
                )
            )
            .verticalScroll(rememberScrollState())
            .padding(24.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        // App Logo and Title
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.padding(bottom = 32.dp)
        ) {
            Text(
                text = "🌸",
                fontSize = 64.sp,
                modifier = Modifier.padding(bottom = 8.dp)
            )
            Text(
                text = "PeriodPal",
                fontSize = 32.sp,
                fontWeight = FontWeight.Bold,
                color = RosePink40
            )
            Text(
                text = "Your personal period tracking companion",
                fontSize = 14.sp,
                color = DarkGray,
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(top = 4.dp)
            )
        }

        // Auth Form Card
        Card(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(20.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Mode Toggle
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.Center
                ) {
                    TextButton(
                        onClick = { 
                            isLoginMode = true
                            errorMessage = ""
                        },
                        colors = ButtonDefaults.textButtonColors(
                            contentColor = if (isLoginMode) RosePink40 else DarkGray
                        )
                    ) {
                        Text(
                            text = "Login",
                            fontWeight = if (isLoginMode) FontWeight.Bold else FontWeight.Normal
                        )
                    }
                    
                    Text(
                        text = " | ",
                        color = DarkGray,
                        modifier = Modifier.padding(horizontal = 8.dp, vertical = 12.dp)
                    )
                    
                    TextButton(
                        onClick = { 
                            isLoginMode = false
                            errorMessage = ""
                        },
                        colors = ButtonDefaults.textButtonColors(
                            contentColor = if (!isLoginMode) RosePink40 else DarkGray
                        )
                    ) {
                        Text(
                            text = "Sign Up",
                            fontWeight = if (!isLoginMode) FontWeight.Bold else FontWeight.Normal
                        )
                    }
                }

                // Display Name Field (Sign Up only)
                if (!isLoginMode) {
                    OutlinedTextField(
                        value = displayName,
                        onValueChange = { displayName = it },
                        label = { Text("Display Name") },
                        leadingIcon = {
                            Icon(
                                imageVector = Icons.Default.Person,
                                contentDescription = "Display Name"
                            )
                        },
                        modifier = Modifier.fillMaxWidth(),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = RosePink40,
                            focusedLabelColor = RosePink40
                        )
                    )
                }

                // Email Field
                OutlinedTextField(
                    value = email,
                    onValueChange = { email = it },
                    label = { Text("Email") },
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Default.Email,
                            contentDescription = "Email"
                        )
                    },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Email),
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = RosePink40,
                        focusedLabelColor = RosePink40
                    )
                )

                // Password Field
                OutlinedTextField(
                    value = password,
                    onValueChange = { password = it },
                    label = { Text("Password") },
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Default.Lock,
                            contentDescription = "Password"
                        )
                    },
                    trailingIcon = {
                        IconButton(onClick = { isPasswordVisible = !isPasswordVisible }) {
                            Icon(
                                imageVector = if (isPasswordVisible) Icons.Default.VisibilityOff else Icons.Default.Visibility,
                                contentDescription = if (isPasswordVisible) "Hide password" else "Show password"
                            )
                        }
                    },
                    visualTransformation = if (isPasswordVisible) VisualTransformation.None else PasswordVisualTransformation(),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Password),
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = RosePink40,
                        focusedLabelColor = RosePink40
                    )
                )

                // Confirm Password Field (Sign Up only)
                if (!isLoginMode) {
                    OutlinedTextField(
                        value = confirmPassword,
                        onValueChange = { confirmPassword = it },
                        label = { Text("Confirm Password") },
                        leadingIcon = {
                            Icon(
                                imageVector = Icons.Default.Lock,
                                contentDescription = "Confirm Password"
                            )
                        },
                        visualTransformation = PasswordVisualTransformation(),
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Password),
                        modifier = Modifier.fillMaxWidth(),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = RosePink40,
                            focusedLabelColor = RosePink40
                        )
                    )
                }

                // Error Message
                if (errorMessage.isNotEmpty() || authError.isNotEmpty()) {
                    Text(
                        text = errorMessage.ifEmpty { authError },
                        color = ErrorRed,
                        fontSize = 12.sp,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.fillMaxWidth()
                    )
                }

                // Submit Button
                Button(
                    onClick = {
                        errorMessage = ""
                        if (isLoginMode) {
                            // Handle login
                            if (email.isNotEmpty() && password.isNotEmpty()) {
                                isLoading = true
                                coroutineScope.launch {
                                    val success = authViewModel.signIn(email, password)
                                    isLoading = false
                                    if (success) {
                                        onLoginSuccess()
                                    }
                                }
                            } else {
                                errorMessage = "Please fill in all fields"
                            }
                        } else {
                            // Handle sign up
                            if (email.isNotEmpty() && password.isNotEmpty() &&
                                displayName.isNotEmpty() && password == confirmPassword) {
                                isLoading = true
                                coroutineScope.launch {
                                    val success = authViewModel.signUp(email, password, displayName)
                                    isLoading = false
                                    if (success) {
                                        onSignUpSuccess()
                                    }
                                }
                            } else if (password != confirmPassword) {
                                errorMessage = "Passwords do not match"
                            } else {
                                errorMessage = "Please fill in all fields"
                            }
                        }
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(56.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = RosePink40
                    ),
                    shape = RoundedCornerShape(12.dp),
                    enabled = !isLoading
                ) {
                    if (isLoading) {
                        CircularProgressIndicator(
                            color = Color.White,
                            modifier = Modifier.size(20.dp)
                        )
                    } else {
                        Text(
                            text = if (isLoginMode) "Login" else "Sign Up",
                            color = Color.White,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }

                // Forgot Password (Login only)
                if (isLoginMode) {
                    TextButton(
                        onClick = { /* TODO: Implement forgot password */ },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text(
                            text = "Forgot Password?",
                            color = RosePink40,
                            fontSize = 14.sp
                        )
                    }
                }
            }
        }

        // Terms and Privacy (Sign Up only)
        if (!isLoginMode) {
            Text(
                text = "By signing up, you agree to our Terms of Service and Privacy Policy",
                fontSize = 12.sp,
                color = DarkGray,
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(top = 16.dp)
            )
        }
    }
}
