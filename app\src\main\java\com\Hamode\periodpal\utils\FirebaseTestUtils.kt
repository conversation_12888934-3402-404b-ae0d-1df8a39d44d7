package com.Hamode.periodpal.utils

import android.util.Log
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore

object FirebaseTestUtils {
    private const val TAG = "FirebaseTest"
    
    fun testFirebaseConnection() {
        try {
            val auth = FirebaseAuth.getInstance()
            val firestore = FirebaseFirestore.getInstance()
            
            Log.d(TAG, "Firebase Auth instance: ${auth.app.name}")
            Log.d(TAG, "Firebase Firestore instance: ${firestore.app.name}")
            Log.d(TAG, "Current user: ${auth.currentUser?.uid ?: "No user logged in"}")
            
            // Test Firestore connection
            firestore.collection("test").document("connection")
                .set(mapOf("timestamp" to System.currentTimeMillis()))
                .addOnSuccessListener {
                    Log.d(TAG, "Firestore connection test successful")
                }
                .addOnFailureListener { e ->
                    Log.e(TAG, "Firestore connection test failed", e)
                }
                
        } catch (e: Exception) {
            Log.e(TAG, "Firebase initialization error", e)
        }
    }
}
