package com.Hamode.periodpal.data.models

import java.time.LocalDate

/**
 * Represents different phases of the menstrual cycle
 */
enum class CyclePhase(val displayName: String, val description: String) {
    MENSTRUAL("Menstrual", "Period days - menstruation phase"),
    FOLLICULAR("Follicular", "Post-period phase - follicle development"),
    OVULATION("Ovulation", "Ovulation phase - most fertile time"),
    LUTEAL("Luteal", "Pre-period phase - corpus luteum phase")
}

/**
 * Represents flow intensity levels
 */
enum class FlowIntensity(val level: Int, val displayName: String, val description: String) {
    NONE(0, "None", "No flow"),
    SPOTTING(1, "Spotting", "Very light spotting"),
    LIGHT(2, "Light", "Light flow"),
    MEDIUM(3, "Medium", "Medium flow"),
    HEAVY(4, "Heavy", "Heavy flow"),
    VERY_HEAVY(5, "Very Heavy", "Very heavy flow")
}

/**
 * Represents pain levels
 */
enum class PainLevel(val level: Int, val displayName: String, val description: String) {
    NONE(0, "None", "No pain"),
    MILD(1, "Mild", "Mild discomfort"),
    MODERATE(2, "Moderate", "Moderate pain"),
    SEVERE(3, "Severe", "Severe pain"),
    EXTREME(4, "Extreme", "Extreme pain")
}

/**
 * Represents mood states
 */
enum class MoodState(val displayName: String, val emoji: String) {
    HAPPY("Happy", "😊"),
    SAD("Sad", "😢"),
    ANXIOUS("Anxious", "😰"),
    IRRITABLE("Irritable", "😤"),
    CALM("Calm", "😌"),
    ENERGETIC("Energetic", "⚡"),
    TIRED("Tired", "😴"),
    EMOTIONAL("Emotional", "🥺")
}

/**
 * Represents common symptoms
 */
enum class Symptom(val displayName: String, val emoji: String, val category: String) {
    CRAMPS("Cramps", "🤕", "Pain"),
    HEADACHE("Headache", "🤯", "Pain"),
    BACK_PAIN("Back Pain", "🦴", "Pain"),
    BREAST_TENDERNESS("Breast Tenderness", "💔", "Physical"),
    BLOATING("Bloating", "🎈", "Physical"),
    ACNE("Acne", "🔴", "Skin"),
    FATIGUE("Fatigue", "😴", "Energy"),
    NAUSEA("Nausea", "🤢", "Digestive"),
    FOOD_CRAVINGS("Food Cravings", "🍫", "Appetite"),
    INSOMNIA("Insomnia", "🌙", "Sleep")
}

/**
 * Daily log entry for tracking
 */
data class DailyLog(
    val date: LocalDate,
    val flowIntensity: FlowIntensity = FlowIntensity.NONE,
    val painLevel: PainLevel = PainLevel.NONE,
    val mood: MoodState? = null,
    val symptoms: List<Symptom> = emptyList(),
    val notes: String = "",
    val temperature: Double? = null,
    val weight: Double? = null,
    val sleepHours: Double? = null,
    val exerciseMinutes: Int? = null,
    val waterGlasses: Int? = null
)

/**
 * Cycle statistics for insights
 */
data class CycleStatistics(
    val averageCycleLength: Double,
    val averagePeriodLength: Double,
    val cycleVariability: Double,
    val totalCycles: Int,
    val longestCycle: Int,
    val shortestCycle: Int,
    val mostCommonSymptoms: List<Symptom>,
    val averagePainLevel: Double,
    val moodTrends: Map<MoodState, Int>
) {
    fun areRegular(): Boolean = cycleVariability <= 3.0
    
    fun getRegularityDescription(): String = when {
        cycleVariability <= 2.0 -> "Very Regular"
        cycleVariability <= 3.0 -> "Regular"
        cycleVariability <= 5.0 -> "Somewhat Irregular"
        else -> "Irregular"
    }
}

/**
 * Cycle prediction data
 */
data class CyclePrediction(
    val nextPeriodStart: LocalDate,
    val nextPeriodEnd: LocalDate,
    val nextFertileWindowStart: LocalDate,
    val nextFertileWindowEnd: LocalDate,
    val nextOvulationDate: LocalDate,
    val nextPMSStart: LocalDate,
    val confidence: Double, // 0.0 to 1.0
    val basedOnCycles: Int
)

/**
 * Health insight types
 */
enum class HealthInsightType(val displayName: String) {
    CYCLE_PATTERN("Cycle Pattern"),
    SYMPTOM_TREND("Symptom Trend"),
    MOOD_PATTERN("Mood Pattern"),
    HEALTH_TIP("Health Tip"),
    PREDICTION("Prediction"),
    WARNING("Warning")
}

/**
 * Health insight severity
 */
enum class HealthInsightSeverity(val displayName: String, val color: String) {
    INFO("Info", "#2196F3"),
    LOW("Low", "#4CAF50"),
    MEDIUM("Medium", "#FF9800"),
    HIGH("High", "#F44336")
}

/**
 * Health insight data
 */
data class HealthInsight(
    val id: String,
    val type: HealthInsightType,
    val severity: HealthInsightSeverity,
    val title: String,
    val description: String,
    val recommendation: String,
    val date: LocalDate,
    val isRead: Boolean = false
)

/**
 * Calendar day data for UI
 */
data class CalendarDayData(
    val date: LocalDate,
    val isToday: Boolean = false,
    val cycleDay: Int? = null,
    val phase: CyclePhase? = null,
    val flowIntensity: FlowIntensity = FlowIntensity.NONE,
    val hasSymptoms: Boolean = false,
    val isPredicted: Boolean = false,
    val isFertile: Boolean = false,
    val isPMS: Boolean = false
)
