<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PeriodPal\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PeriodPal\app\src\main\res"><file name="ic_launcher_background" path="D:\PeriodPal\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="D:\PeriodPal\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_launcher" path="D:\PeriodPal\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="D:\PeriodPal\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="D:\PeriodPal\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\PeriodPal\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\PeriodPal\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\PeriodPal\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\PeriodPal\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\PeriodPal\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\PeriodPal\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\PeriodPal\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\PeriodPal\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\PeriodPal\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="D:\PeriodPal\app\src\main\res\values\colors.xml" qualifiers=""><color name="rose_pink_80">#FFF8BBD9</color><color name="rose_pink_60">#FFF48FB1</color><color name="rose_pink_40">#FFE91E63</color><color name="rose_pink_20">#FFAD1457</color><color name="soft_pink_80">#FFFCE4EC</color><color name="soft_pink_60">#FFF8BBD9</color><color name="soft_pink_40">#FFF48FB1</color><color name="soft_pink_20">#FFE91E63</color><color name="white">#FFFFFFFF</color><color name="off_white">#FFFFFBFE</color><color name="light_gray">#FFF5F5F5</color><color name="medium_gray">#FFE0E0E0</color><color name="dark_gray">#FF424242</color><color name="black">#FF000000</color><color name="fertile_green">#FF4CAF50</color><color name="pms_purple">#FF9C27B0</color><color name="period_red">#FFE53935</color><color name="ovulation_blue">#FF2196F3</color><color name="success_green">#FF4CAF50</color><color name="warning_orange">#FFFF9800</color><color name="error_red">#FFF44336</color><color name="info_blue">#FF2196F3</color><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color></file><file path="D:\PeriodPal\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">PeriodPal</string></file><file path="D:\PeriodPal\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.PeriodPal" parent="android:Theme.Material.Light.NoActionBar"/></file><file name="backup_rules" path="D:\PeriodPal\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="D:\PeriodPal\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PeriodPal\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PeriodPal\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PeriodPal\app\build\generated\res\resValues\debug"/><source path="D:\PeriodPal\app\build\generated\res\processDebugGoogleServices"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PeriodPal\app\build\generated\res\resValues\debug"/><source path="D:\PeriodPal\app\build\generated\res\processDebugGoogleServices"><file path="D:\PeriodPal\app\build\generated\res\processDebugGoogleServices\values\values.xml" qualifiers=""><string name="gcm_defaultSenderId" translatable="false">48446210864</string><string name="google_api_key" translatable="false">AIzaSyCZbux9qJJ9gqgX5Zz5heqTqBiXwAZCNXQ</string><string name="google_app_id" translatable="false">1:48446210864:android:f2f42aca45a2e2bbaf7ef5</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyCZbux9qJJ9gqgX5Zz5heqTqBiXwAZCNXQ</string><string name="google_storage_bucket" translatable="false">periodpal-bfb8c.firebasestorage.app</string><string name="project_id" translatable="false">periodpal-bfb8c</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices" generated-set="res-processDebugGoogleServices$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>