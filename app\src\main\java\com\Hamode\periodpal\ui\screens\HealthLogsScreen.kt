package com.Hamode.periodpal.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.Hamode.periodpal.data.models.*
import com.Hamode.periodpal.ui.theme.*
import java.time.LocalDate
import java.time.format.DateTimeFormatter

@Composable
fun HealthLogsScreen(
    modifier: Modifier = Modifier
) {
    var selectedDate by remember { mutableStateOf(LocalDate.now()) }
    var currentLog by remember { mutableStateOf(DailyLog(date = selectedDate)) }
    var showDatePicker by remember { mutableStateOf(false) }

    Column(
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background)
            .padding(16.dp)
    ) {
        // Header with Date Selection
        DateSelectionHeader(
            selectedDate = selectedDate,
            onDateSelected = { date ->
                selectedDate = date
                currentLog = currentLog.copy(date = date)
            },
            onShowDatePicker = { showDatePicker = true },
            modifier = Modifier.fillMaxWidth()
        )

        Spacer(modifier = Modifier.height(16.dp))

        // Scrollable Content
        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(16.dp),
            modifier = Modifier.fillMaxSize()
        ) {
            // Flow Intensity Section
            item {
                FlowIntensitySection(
                    selectedIntensity = currentLog.flowIntensity,
                    onIntensitySelected = { intensity ->
                        currentLog = currentLog.copy(flowIntensity = intensity)
                    }
                )
            }

            // Pain Level Section
            item {
                PainLevelSection(
                    selectedLevel = currentLog.painLevel,
                    onLevelSelected = { level ->
                        currentLog = currentLog.copy(painLevel = level)
                    }
                )
            }

            // Mood Section
            item {
                LoggingSection(
                    title = "Mood",
                    icon = Icons.Default.Mood
                ) {
                    Text(
                        text = "Mood tracking coming soon!",
                        color = DarkGray,
                        modifier = Modifier.padding(16.dp)
                    )
                }
            }

            // Symptoms Section
            item {
                LoggingSection(
                    title = "Symptoms",
                    icon = Icons.Default.HealthAndSafety
                ) {
                    Text(
                        text = "Symptom logging coming soon!",
                        color = DarkGray,
                        modifier = Modifier.padding(16.dp)
                    )
                }
            }

            // Notes Section
            item {
                LoggingSection(
                    title = "Notes",
                    icon = Icons.Default.Notes
                ) {
                    OutlinedTextField(
                        value = currentLog.notes,
                        onValueChange = { notes ->
                            currentLog = currentLog.copy(notes = notes)
                        },
                        placeholder = { Text("Add any additional notes about your day...") },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(100.dp),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = RosePink40,
                            focusedLabelColor = RosePink40
                        ),
                        maxLines = 4
                    )
                }
            }

            // Save Button
            item {
                Button(
                    onClick = {
                        // TODO: Save to Firebase
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(56.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = RosePink40
                    ),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Save,
                            contentDescription = "Save",
                            tint = Color.White
                        )
                        Text(
                            text = "Save Health Log",
                            color = Color.White,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }

            // Bottom spacing
            item {
                Spacer(modifier = Modifier.height(80.dp))
            }
        }
    }
}

@Composable
private fun DateSelectionHeader(
    selectedDate: LocalDate,
    onDateSelected: (LocalDate) -> Unit,
    onShowDatePicker: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = RosePink40
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column {
                Text(
                    text = "Health Log",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
                Text(
                    text = selectedDate.format(DateTimeFormatter.ofPattern("EEEE, MMM dd, yyyy")),
                    fontSize = 14.sp,
                    color = Color.White.copy(alpha = 0.9f)
                )
            }

            Row {
                IconButton(
                    onClick = { onDateSelected(selectedDate.minusDays(1)) }
                ) {
                    Icon(
                        imageVector = Icons.Default.ChevronLeft,
                        contentDescription = "Previous Day",
                        tint = Color.White
                    )
                }
                IconButton(onClick = onShowDatePicker) {
                    Icon(
                        imageVector = Icons.Default.CalendarToday,
                        contentDescription = "Select Date",
                        tint = Color.White
                    )
                }
                IconButton(
                    onClick = { onDateSelected(selectedDate.plusDays(1)) }
                ) {
                    Icon(
                        imageVector = Icons.Default.ChevronRight,
                        contentDescription = "Next Day",
                        tint = Color.White
                    )
                }
            }
        }
    }
}

@Composable
private fun FlowIntensitySection(
    selectedIntensity: FlowIntensity,
    onIntensitySelected: (FlowIntensity) -> Unit,
    modifier: Modifier = Modifier
) {
    LoggingSection(
        title = "Flow Intensity",
        icon = Icons.Default.Opacity,
        modifier = modifier
    ) {
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            modifier = Modifier.fillMaxWidth()
        ) {
            items(FlowIntensity.values()) { intensity ->
                FlowIntensityChip(
                    intensity = intensity,
                    isSelected = selectedIntensity == intensity,
                    onClick = { onIntensitySelected(intensity) }
                )
            }
        }
    }
}

@Composable
private fun FlowIntensityChip(
    intensity: FlowIntensity,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val backgroundColor = if (isSelected) {
        PeriodPalThemeColors.getFlowColor(intensity)
    } else {
        LightGray
    }

    Card(
        modifier = modifier
            .clickable { onClick() },
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(
            containerColor = backgroundColor
        )
    ) {
        Column(
            modifier = Modifier.padding(12.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = intensity.level.toString(),
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = if (isSelected) Color.White else DarkGray
            )
            Text(
                text = intensity.displayName,
                fontSize = 10.sp,
                color = if (isSelected) Color.White else DarkGray,
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
private fun PainLevelSection(
    selectedLevel: PainLevel,
    onLevelSelected: (PainLevel) -> Unit,
    modifier: Modifier = Modifier
) {
    LoggingSection(
        title = "Pain Level",
        icon = Icons.Default.Healing,
        modifier = modifier
    ) {
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            modifier = Modifier.fillMaxWidth()
        ) {
            items(PainLevel.values()) { level ->
                PainLevelChip(
                    level = level,
                    isSelected = selectedLevel == level,
                    onClick = { onLevelSelected(level) }
                )
            }
        }
    }
}

@Composable
private fun PainLevelChip(
    level: PainLevel,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val backgroundColor = if (isSelected) {
        PeriodPalThemeColors.getPainColor(level)
    } else {
        LightGray
    }

    Card(
        modifier = modifier
            .clickable { onClick() },
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(
            containerColor = backgroundColor
        )
    ) {
        Column(
            modifier = Modifier.padding(12.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = level.level.toString(),
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = if (isSelected) Color.White else DarkGray
            )
            Text(
                text = level.displayName,
                fontSize = 10.sp,
                color = if (isSelected) Color.White else DarkGray,
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
private fun LoggingSection(
    title: String,
    icon: ImageVector,
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(bottom = 12.dp)
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = title,
                    tint = RosePink40,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.onSurface
                )
            }
            content()
        }
    }
}
