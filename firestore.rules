rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Period data - users can only access their own period records
    match /users/{userId}/periods/{periodId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Symptoms data - users can only access their own symptom records
    match /users/{userId}/symptoms/{symptomId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Moods data - users can only access their own mood records
    match /users/{userId}/moods/{moodId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Notes data - users can only access their own notes
    match /users/{userId}/notes/{noteId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Medications data - users can only access their own medication records
    match /users/{userId}/medications/{medicationId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Health insights data - users can only access their own insights
    match /users/{userId}/insights/{insightId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Backup data - users can only access their own backups
    match /users/{userId}/backups/{backupId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
