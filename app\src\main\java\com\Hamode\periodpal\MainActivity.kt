package com.Hamode.periodpal

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.Hamode.periodpal.ui.theme.PeriodPalTheme

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            PeriodPalTheme {
                PeriodPalApp()
            }
        }
    }
}

@Composable
fun PeriodPalApp() {
    val authViewModel = remember { com.Hamode.periodpal.ui.viewmodel.AuthViewModel() }
    val authState by authViewModel.authState.collectAsState()

    when (authState) {
        is com.Hamode.periodpal.ui.viewmodel.AuthState.Loading -> {
            LoadingScreen()
        }
        is com.Hamode.periodpal.ui.viewmodel.AuthState.Unauthenticated -> {
            com.Hamode.periodpal.ui.screens.AuthScreen(
                onLoginSuccess = { /* Auth handled by ViewModel */ },
                onSignUpSuccess = { /* Auth handled by ViewModel */ }
            )
        }
        is com.Hamode.periodpal.ui.viewmodel.AuthState.Authenticated -> {
            MainAppContent()
        }
    }
}

@Composable
fun LoadingScreen() {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        Color.White,
                        com.Hamode.periodpal.ui.theme.SoftPink80.copy(alpha = 0.3f)
                    )
                )
            ),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = "🌸",
                fontSize = 64.sp
            )
            CircularProgressIndicator(
                color = com.Hamode.periodpal.ui.theme.RosePink40
            )
            Text(
                text = "Loading PeriodPal...",
                color = com.Hamode.periodpal.ui.theme.DarkGray,
                fontSize = 16.sp
            )
        }
    }
}

@Composable
fun MainAppContent() {
    var currentDestination by remember { mutableStateOf(com.Hamode.periodpal.ui.navigation.NavigationDestination.HOME) }

    Scaffold(
        modifier = Modifier.fillMaxSize(),
        bottomBar = {
            com.Hamode.periodpal.ui.components.PeriodPalBottomNavigation(
                currentDestination = currentDestination,
                onNavigate = { destination ->
                    currentDestination = destination
                }
            )
        }
    ) { innerPadding ->
        when (currentDestination) {
            com.Hamode.periodpal.ui.navigation.NavigationDestination.HOME -> {
                com.Hamode.periodpal.ui.screens.HomeScreen(
                    onNavigateToCalendar = {
                        currentDestination = com.Hamode.periodpal.ui.navigation.NavigationDestination.CALENDAR
                    },
                    onNavigateToLogs = {
                        currentDestination = com.Hamode.periodpal.ui.navigation.NavigationDestination.HEALTH_LOGS
                    },
                    onNavigateToInsights = {
                        currentDestination = com.Hamode.periodpal.ui.navigation.NavigationDestination.INSIGHTS
                    },
                    modifier = Modifier.padding(innerPadding)
                )
            }
            com.Hamode.periodpal.ui.navigation.NavigationDestination.CALENDAR -> {
                com.Hamode.periodpal.ui.screens.CalendarScreen(
                    modifier = Modifier.padding(innerPadding)
                )
            }
            com.Hamode.periodpal.ui.navigation.NavigationDestination.HEALTH_LOGS -> {
                com.Hamode.periodpal.ui.screens.HealthLogsScreen(
                    modifier = Modifier.padding(innerPadding)
                )
            }
            com.Hamode.periodpal.ui.navigation.NavigationDestination.INSIGHTS -> {
                com.Hamode.periodpal.ui.screens.HealthInsightsScreen(
                    modifier = Modifier.padding(innerPadding)
                )
            }
            com.Hamode.periodpal.ui.navigation.NavigationDestination.PROFILE -> {
                com.Hamode.periodpal.ui.screens.ProfileScreen(
                    onSignOut = { /* TODO: Handle sign out */ },
                    modifier = Modifier.padding(innerPadding)
                )
            }
        }
    }
}

@Composable
fun PlaceholderScreen(
    title: String,
    description: String,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxSize()
            .padding(32.dp),
        contentAlignment = androidx.compose.ui.Alignment.Center
    ) {
        Column(
            horizontalAlignment = androidx.compose.ui.Alignment.CenterHorizontally,
            verticalArrangement = androidx.compose.foundation.layout.Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.headlineMedium,
                color = com.Hamode.periodpal.ui.theme.RosePink40
            )
            Text(
                text = description,
                style = MaterialTheme.typography.bodyLarge,
                color = com.Hamode.periodpal.ui.theme.DarkGray,
                textAlign = androidx.compose.ui.text.style.TextAlign.Center
            )
            Text(
                text = "Coming Soon! 🌸",
                style = MaterialTheme.typography.bodyMedium,
                color = com.Hamode.periodpal.ui.theme.RosePink40
            )
        }
    }
}

@Composable
fun PeriodPalSimpleTheme(content: @Composable () -> Unit) {
    MaterialTheme(
        colorScheme = lightColorScheme(
            primary = Color(0xFFE91E63),
            secondary = Color(0xFFFCE4EC),
            background = Color.White,
            surface = Color.White
        ),
        content = content
    )
}

@Composable
fun PeriodPalMainScreen() {
    val rosePink = Color(0xFFE91E63)
    val softPink = Color(0xFFFCE4EC)
    
    Scaffold(
        modifier = Modifier.fillMaxSize()
    ) { innerPadding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    brush = Brush.verticalGradient(
                        colors = listOf(
                            Color.White,
                            softPink
                        )
                    )
                )
                .padding(innerPadding),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(24.dp),
                modifier = Modifier.padding(32.dp)
            ) {
                // App Title
                Text(
                    text = "🌸 PeriodPal",
                    fontSize = 32.sp,
                    fontWeight = FontWeight.Bold,
                    color = rosePink,
                    textAlign = TextAlign.Center
                )
                
                // Welcome Card
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(20.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    ),
                    elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(24.dp),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.spacedBy(16.dp)
                    ) {
                        Text(
                            text = "Welcome to PeriodPal!",
                            fontSize = 24.sp,
                            fontWeight = FontWeight.SemiBold,
                            color = rosePink,
                            textAlign = TextAlign.Center
                        )
                        
                        Text(
                            text = "Your personal period tracking companion with beautiful, accessible design.",
                            fontSize = 16.sp,
                            color = Color.Gray,
                            textAlign = TextAlign.Center,
                            lineHeight = 24.sp
                        )
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        Button(
                            onClick = { /* TODO: Navigate to main app */ },
                            modifier = Modifier.fillMaxWidth(),
                            colors = ButtonDefaults.buttonColors(
                                containerColor = rosePink
                            ),
                            shape = RoundedCornerShape(12.dp)
                        ) {
                            Text(
                                text = "Get Started",
                                color = Color.White,
                                fontWeight = FontWeight.Medium,
                                modifier = Modifier.padding(8.dp)
                            )
                        }
                    }
                }
                
                // Features Preview
                Text(
                    text = "✨ Features:\n• Cycle Tracking\n• Symptom Logging\n• Health Insights\n• Accessibility Support",
                    fontSize = 14.sp,
                    color = Color.Gray,
                    textAlign = TextAlign.Center,
                    lineHeight = 20.sp
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun PeriodPalMainScreenPreview() {
    PeriodPalSimpleTheme {
        PeriodPalMainScreen()
    }
}
