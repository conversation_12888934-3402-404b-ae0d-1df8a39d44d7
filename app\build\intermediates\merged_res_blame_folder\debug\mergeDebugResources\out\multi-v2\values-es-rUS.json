{"logs": [{"outputFile": "com.Hamode.periodpal.app-mergeDebugResources-61:/values-es-rUS/values-es-rUS.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b3163db060619b51a72cfb285e5abcba\\transformed\\play-services-basement-18.4.0\\res\\values-es-rUS\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "141", "endOffsets": "340"}, "to": {"startLines": "21", "startColumns": "4", "startOffsets": "2289", "endColumns": "145", "endOffsets": "2430"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e8b5bc1662136e087738bd938abfe3e7\\transformed\\credentials-1.3.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,169", "endColumns": "113,121", "endOffsets": "164,286"}, "to": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "105,219", "endColumns": "113,121", "endOffsets": "214,336"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9cef95d772ca94c574c1d7981123fcc6\\transformed\\play-services-base-18.5.0\\res\\values-es-rUS\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,301,463,591,695,852,979,1098,1200,1367,1472,1638,1767,1940,2114,2180,2238", "endColumns": "103,161,127,103,156,126,118,101,166,104,165,128,172,173,65,57,73", "endOffsets": "300,462,590,694,851,978,1097,1199,1366,1471,1637,1766,1939,2113,2179,2237,2311"}, "to": {"startLines": "13,14,15,16,17,18,19,20,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1254,1362,1528,1660,1768,1929,2060,2183,2435,2606,2715,2885,3018,3195,3373,3443,3505", "endColumns": "107,165,131,107,160,130,122,105,170,108,169,132,176,177,69,61,77", "endOffsets": "1357,1523,1655,1763,1924,2055,2178,2284,2601,2710,2880,3013,3190,3368,3438,3500,3578"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\11cdba141bdfb547960b7fcdc0b4367a\\transformed\\ui-release\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,286,384,487,576,655,751,843,930,1017,1107,1184,1260,1340,1416,1494,1564", "endColumns": "98,81,97,102,88,78,95,91,86,86,89,76,75,79,75,77,69,122", "endOffsets": "199,281,379,482,571,650,746,838,925,1012,1102,1179,1255,1335,1411,1489,1559,1682"}, "to": {"startLines": "11,12,32,33,34,38,39,97,98,99,100,101,102,103,104,106,107,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1073,1172,3692,3790,3893,4305,4384,10841,10933,11020,11107,11197,11274,11350,11430,11607,11685,11755", "endColumns": "98,81,97,102,88,78,95,91,86,86,89,76,75,79,75,77,69,122", "endOffsets": "1167,1249,3785,3888,3977,4379,4475,10928,11015,11102,11192,11269,11345,11425,11501,11680,11750,11873"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e9aad415eee13c5225ceb41d0c3631e4\\transformed\\foundation-release\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,155", "endColumns": "99,101", "endOffsets": "150,252"}, "to": {"startLines": "109,110", "startColumns": "4,4", "startOffsets": "11878,11978", "endColumns": "99,101", "endOffsets": "11973,12075"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4a4406fb22280fb302c67f992ef8133c\\transformed\\material3-release\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,177,299,422,542,642,740,855,998,1116,1268,1353,1455,1552,1654,1772,1895,2002,2138,2271,2410,2592,2723,2843,2965,3092,3190,3286,3407,3540,3641,3746,3861,3996,4137,4248,4353,4430,4526,4621,4742,4829,4918,5029,5109,5193,5294,5400,5500,5599,5687,5802,5903,6007,6130,6210,6317", "endColumns": "121,121,122,119,99,97,114,142,117,151,84,101,96,101,117,122,106,135,132,138,181,130,119,121,126,97,95,120,132,100,104,114,134,140,110,104,76,95,94,120,86,88,110,79,83,100,105,99,98,87,114,100,103,122,79,106,98", "endOffsets": "172,294,417,537,637,735,850,993,1111,1263,1348,1450,1547,1649,1767,1890,1997,2133,2266,2405,2587,2718,2838,2960,3087,3185,3281,3402,3535,3636,3741,3856,3991,4132,4243,4348,4425,4521,4616,4737,4824,4913,5024,5104,5188,5289,5395,5495,5594,5682,5797,5898,6002,6125,6205,6312,6411"}, "to": {"startLines": "40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4480,4602,4724,4847,4967,5067,5165,5280,5423,5541,5693,5778,5880,5977,6079,6197,6320,6427,6563,6696,6835,7017,7148,7268,7390,7517,7615,7711,7832,7965,8066,8171,8286,8421,8562,8673,8778,8855,8951,9046,9167,9254,9343,9454,9534,9618,9719,9825,9925,10024,10112,10227,10328,10432,10555,10635,10742", "endColumns": "121,121,122,119,99,97,114,142,117,151,84,101,96,101,117,122,106,135,132,138,181,130,119,121,126,97,95,120,132,100,104,114,134,140,110,104,76,95,94,120,86,88,110,79,83,100,105,99,98,87,114,100,103,122,79,106,98", "endOffsets": "4597,4719,4842,4962,5062,5160,5275,5418,5536,5688,5773,5875,5972,6074,6192,6315,6422,6558,6691,6830,7012,7143,7263,7385,7512,7610,7706,7827,7960,8061,8166,8281,8416,8557,8668,8773,8850,8946,9041,9162,9249,9338,9449,9529,9613,9714,9820,9920,10019,10107,10222,10323,10427,10550,10630,10737,10836"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c2e988808bb7f73a8315e368f7c5f6e3\\transformed\\core-1.13.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "4,5,6,7,8,9,10,105", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "341,440,542,642,740,847,953,11506", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "435,537,637,735,842,948,1068,11602"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9f3b0e192fae8d81415bbd16b5199dc2\\transformed\\browser-1.4.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,164,266,381", "endColumns": "108,101,114,105", "endOffsets": "159,261,376,482"}, "to": {"startLines": "31,35,36,37", "startColumns": "4,4,4,4", "startOffsets": "3583,3982,4084,4199", "endColumns": "108,101,114,105", "endOffsets": "3687,4079,4194,4300"}}]}]}