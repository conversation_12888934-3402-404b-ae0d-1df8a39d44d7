/ Header Record For PersistentHashMapValueStorage$ #androidx.activity.ComponentActivity kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel, +com.Hamode.periodpal.ui.viewmodel.AuthState, +com.Hamode.periodpal.ui.viewmodel.AuthState, +com.Hamode.periodpal.ui.viewmodel.AuthState$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity