1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.Hamode.periodpal"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:25:5-67
11-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:25:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:26:5-79
12-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:26:22-76
13    <uses-permission android:name="android.permission.WAKE_LOCK" />
13-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:24:5-68
13-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:24:22-65
14    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
14-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:25:5-79
14-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:25:22-76
15    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
15-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:26:5-88
15-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:26:22-85
16    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
16-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:27:5-82
16-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:27:22-79
17    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
17-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:26:5-110
17-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:26:22-107
18    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
18-->[com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4aec7283bb19438e6d0e52216495fe95\transformed\recaptcha-18.5.1\AndroidManifest.xml:9:5-98
18-->[com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4aec7283bb19438e6d0e52216495fe95\transformed\recaptcha-18.5.1\AndroidManifest.xml:9:22-95
19
20    <permission
20-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2e988808bb7f73a8315e368f7c5f6e3\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
21        android:name="com.Hamode.periodpal.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
21-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2e988808bb7f73a8315e368f7c5f6e3\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
22        android:protectionLevel="signature" />
22-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2e988808bb7f73a8315e368f7c5f6e3\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
23
24    <uses-permission android:name="com.Hamode.periodpal.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
24-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2e988808bb7f73a8315e368f7c5f6e3\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
24-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2e988808bb7f73a8315e368f7c5f6e3\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
25
26    <application
26-->D:\PeriodPal\app\src\main\AndroidManifest.xml:5:5-26:19
27        android:allowBackup="true"
27-->D:\PeriodPal\app\src\main\AndroidManifest.xml:6:9-35
28        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
28-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c2e988808bb7f73a8315e368f7c5f6e3\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
29        android:dataExtractionRules="@xml/data_extraction_rules"
29-->D:\PeriodPal\app\src\main\AndroidManifest.xml:7:9-65
30        android:debuggable="true"
31        android:extractNativeLibs="false"
32        android:fullBackupContent="@xml/backup_rules"
32-->D:\PeriodPal\app\src\main\AndroidManifest.xml:8:9-54
33        android:icon="@mipmap/ic_launcher"
33-->D:\PeriodPal\app\src\main\AndroidManifest.xml:9:9-43
34        android:label="@string/app_name"
34-->D:\PeriodPal\app\src\main\AndroidManifest.xml:10:9-41
35        android:roundIcon="@mipmap/ic_launcher_round"
35-->D:\PeriodPal\app\src\main\AndroidManifest.xml:11:9-54
36        android:supportsRtl="true"
36-->D:\PeriodPal\app\src\main\AndroidManifest.xml:12:9-35
37        android:theme="@style/Theme.PeriodPal" >
37-->D:\PeriodPal\app\src\main\AndroidManifest.xml:13:9-47
38        <activity
38-->D:\PeriodPal\app\src\main\AndroidManifest.xml:15:9-25:20
39            android:name="com.Hamode.periodpal.MainActivity"
39-->D:\PeriodPal\app\src\main\AndroidManifest.xml:16:13-41
40            android:exported="true"
40-->D:\PeriodPal\app\src\main\AndroidManifest.xml:17:13-36
41            android:label="@string/app_name"
41-->D:\PeriodPal\app\src\main\AndroidManifest.xml:18:13-45
42            android:theme="@style/Theme.PeriodPal" >
42-->D:\PeriodPal\app\src\main\AndroidManifest.xml:19:13-51
43            <intent-filter>
43-->D:\PeriodPal\app\src\main\AndroidManifest.xml:20:13-24:29
44                <action android:name="android.intent.action.MAIN" />
44-->D:\PeriodPal\app\src\main\AndroidManifest.xml:21:17-69
44-->D:\PeriodPal\app\src\main\AndroidManifest.xml:21:25-66
45
46                <category android:name="android.intent.category.LAUNCHER" />
46-->D:\PeriodPal\app\src\main\AndroidManifest.xml:23:17-77
46-->D:\PeriodPal\app\src\main\AndroidManifest.xml:23:27-74
47            </intent-filter>
48        </activity>
49
50        <service
50-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94f01784cc2b79a9eba855171679aebb\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:8:9-14:19
51            android:name="com.google.firebase.components.ComponentDiscoveryService"
51-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94f01784cc2b79a9eba855171679aebb\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:9:13-84
52            android:directBootAware="true"
52-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
53            android:exported="false" >
53-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94f01784cc2b79a9eba855171679aebb\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:10:13-37
54            <meta-data
54-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94f01784cc2b79a9eba855171679aebb\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:11:13-13:85
55                android:name="com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthLegacyRegistrar"
55-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94f01784cc2b79a9eba855171679aebb\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:12:17-119
56                android:value="com.google.firebase.components.ComponentRegistrar" />
56-->[com.google.firebase:firebase-auth-ktx:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94f01784cc2b79a9eba855171679aebb\transformed\firebase-auth-ktx-23.1.0\AndroidManifest.xml:13:17-82
57            <meta-data
57-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:69:13-71:85
58                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
58-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:70:17-109
59                android:value="com.google.firebase.components.ComponentRegistrar" />
59-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:71:17-82
60            <meta-data
60-->[com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d26a6119b604e6d19fdfc0cd991336c8\transformed\firebase-analytics-ktx-22.1.2\AndroidManifest.xml:11:13-13:85
61                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar"
61-->[com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d26a6119b604e6d19fdfc0cd991336c8\transformed\firebase-analytics-ktx-22.1.2\AndroidManifest.xml:12:17-129
62                android:value="com.google.firebase.components.ComponentRegistrar" />
62-->[com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d26a6119b604e6d19fdfc0cd991336c8\transformed\firebase-analytics-ktx-22.1.2\AndroidManifest.xml:13:17-82
63            <meta-data
63-->[com.google.firebase:firebase-storage-ktx:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b620626b012543ada914786fa02c5c7\transformed\firebase-storage-ktx-21.0.1\AndroidManifest.xml:12:13-14:85
64                android:name="com.google.firebase.components:com.google.firebase.storage.ktx.FirebaseStorageLegacyRegistrar"
64-->[com.google.firebase:firebase-storage-ktx:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b620626b012543ada914786fa02c5c7\transformed\firebase-storage-ktx-21.0.1\AndroidManifest.xml:13:17-125
65                android:value="com.google.firebase.components.ComponentRegistrar" />
65-->[com.google.firebase:firebase-storage-ktx:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b620626b012543ada914786fa02c5c7\transformed\firebase-storage-ktx-21.0.1\AndroidManifest.xml:14:17-82
66            <meta-data
66-->[com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5848ce010d9af4d93657533a03429814\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:12:13-14:85
67                android:name="com.google.firebase.components:com.google.firebase.firestore.ktx.FirebaseFirestoreLegacyRegistrar"
67-->[com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5848ce010d9af4d93657533a03429814\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:13:17-129
68                android:value="com.google.firebase.components.ComponentRegistrar" />
68-->[com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5848ce010d9af4d93657533a03429814\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:14:17-82
69            <meta-data
69-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09a8e16ba0fe72f59aed493c8b7d58e0\transformed\firebase-storage-21.0.1\AndroidManifest.xml:30:13-32:85
70                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
70-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09a8e16ba0fe72f59aed493c8b7d58e0\transformed\firebase-storage-21.0.1\AndroidManifest.xml:31:17-118
71                android:value="com.google.firebase.components.ComponentRegistrar" />
71-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09a8e16ba0fe72f59aed493c8b7d58e0\transformed\firebase-storage-21.0.1\AndroidManifest.xml:32:17-82
72            <meta-data
72-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09a8e16ba0fe72f59aed493c8b7d58e0\transformed\firebase-storage-21.0.1\AndroidManifest.xml:33:13-35:85
73                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
73-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09a8e16ba0fe72f59aed493c8b7d58e0\transformed\firebase-storage-21.0.1\AndroidManifest.xml:34:17-107
74                android:value="com.google.firebase.components.ComponentRegistrar" />
74-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09a8e16ba0fe72f59aed493c8b7d58e0\transformed\firebase-storage-21.0.1\AndroidManifest.xml:35:17-82
75            <meta-data
75-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69064c91bc7e46e937527784b64a613d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:17:13-19:85
76                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
76-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69064c91bc7e46e937527784b64a613d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:18:17-122
77                android:value="com.google.firebase.components.ComponentRegistrar" />
77-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69064c91bc7e46e937527784b64a613d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:19:17-82
78            <meta-data
78-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69064c91bc7e46e937527784b64a613d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:20:13-22:85
79                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
79-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69064c91bc7e46e937527784b64a613d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:21:17-111
80                android:value="com.google.firebase.components.ComponentRegistrar" />
80-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69064c91bc7e46e937527784b64a613d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:22:17-82
81            <meta-data
81-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56a5e25c257d00e079c8e5b92f87e519\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:25:13-27:85
82                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
82-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56a5e25c257d00e079c8e5b92f87e519\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:26:17-120
83                android:value="com.google.firebase.components.ComponentRegistrar" />
83-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56a5e25c257d00e079c8e5b92f87e519\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:27:17-82
84            <meta-data
84-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56a5e25c257d00e079c8e5b92f87e519\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:28:13-30:85
85                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
85-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56a5e25c257d00e079c8e5b92f87e519\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:29:17-117
86                android:value="com.google.firebase.components.ComponentRegistrar" />
86-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56a5e25c257d00e079c8e5b92f87e519\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:30:17-82
87            <meta-data
87-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:37:13-39:85
88                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
88-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:38:17-139
89                android:value="com.google.firebase.components.ComponentRegistrar" />
89-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:39:17-82
90            <meta-data
90-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0bee4dfb4ff2210a03fbf849e493a40\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
91                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
91-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0bee4dfb4ff2210a03fbf849e493a40\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
92                android:value="com.google.firebase.components.ComponentRegistrar" />
92-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0bee4dfb4ff2210a03fbf849e493a40\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
93            <meta-data
93-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0bee4dfb4ff2210a03fbf849e493a40\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
94                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
94-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0bee4dfb4ff2210a03fbf849e493a40\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
95                android:value="com.google.firebase.components.ComponentRegistrar" />
95-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c0bee4dfb4ff2210a03fbf849e493a40\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
96            <meta-data
96-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a8a49351139c0acd4d23330a6344893\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
97                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
97-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a8a49351139c0acd4d23330a6344893\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
98                android:value="com.google.firebase.components.ComponentRegistrar" />
98-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a8a49351139c0acd4d23330a6344893\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
99            <meta-data
99-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
100                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
100-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
101                android:value="com.google.firebase.components.ComponentRegistrar" />
101-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
102        </service>
103
104        <activity
104-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:29:9-46:20
105            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
105-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:30:13-80
106            android:excludeFromRecents="true"
106-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:31:13-46
107            android:exported="true"
107-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:32:13-36
108            android:launchMode="singleTask"
108-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:33:13-44
109            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
109-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:34:13-72
110            <intent-filter>
110-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:35:13-45:29
111                <action android:name="android.intent.action.VIEW" />
111-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:17-69
111-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:25-66
112
113                <category android:name="android.intent.category.DEFAULT" />
113-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:17-76
113-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:27-73
114                <category android:name="android.intent.category.BROWSABLE" />
114-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:17-78
114-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:27-75
115
116                <data
116-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:41:17-44:51
117                    android:host="firebase.auth"
117-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:42:21-49
118                    android:path="/"
118-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:43:21-37
119                    android:scheme="genericidp" />
119-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:44:21-48
120            </intent-filter>
121        </activity>
122        <activity
122-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:47:9-64:20
123            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
123-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:48:13-79
124            android:excludeFromRecents="true"
124-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:49:13-46
125            android:exported="true"
125-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:50:13-36
126            android:launchMode="singleTask"
126-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:51:13-44
127            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
127-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:52:13-72
128            <intent-filter>
128-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:53:13-63:29
129                <action android:name="android.intent.action.VIEW" />
129-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:17-69
129-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:25-66
130
131                <category android:name="android.intent.category.DEFAULT" />
131-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:17-76
131-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:27-73
132                <category android:name="android.intent.category.BROWSABLE" />
132-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:17-78
132-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:27-75
133
134                <data
134-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:41:17-44:51
135                    android:host="firebase.auth"
135-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:42:21-49
136                    android:path="/"
136-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:43:21-37
137                    android:scheme="recaptcha" />
137-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b78a14e36209544ba765149bc1b0b7cb\transformed\firebase-auth-23.1.0\AndroidManifest.xml:44:21-48
138            </intent-filter>
139        </activity>
140
141        <service
141-->[androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:24:9-32:19
142            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
142-->[androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:25:13-94
143            android:enabled="true"
143-->[androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:26:13-35
144            android:exported="false" >
144-->[androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:27:13-37
145            <meta-data
145-->[androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:29:13-31:104
146                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
146-->[androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:30:17-76
147                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
147-->[androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:31:17-101
148        </service>
149
150        <activity
150-->[androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:34:9-41:20
151            android:name="androidx.credentials.playservices.HiddenActivity"
151-->[androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:35:13-76
152            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
152-->[androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:36:13-87
153            android:enabled="true"
153-->[androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:37:13-35
154            android:exported="false"
154-->[androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:38:13-37
155            android:fitsSystemWindows="true"
155-->[androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:39:13-45
156            android:theme="@style/Theme.Hidden" >
156-->[androidx.credentials:credentials-play-services-auth:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7e756cc255ba5643991ca2f73ae241a\transformed\credentials-play-services-auth-1.3.0\AndroidManifest.xml:40:13-48
157        </activity>
158        <activity
158-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7f12c724be602f5926a30f7f579dcb3\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
159            android:name="androidx.compose.ui.tooling.PreviewActivity"
159-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7f12c724be602f5926a30f7f579dcb3\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
160            android:exported="true" />
160-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7f12c724be602f5926a30f7f579dcb3\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
161        <activity
161-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44341f99e7e4cdc517e2706f1a3795b1\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:23:9-25:39
162            android:name="androidx.activity.ComponentActivity"
162-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44341f99e7e4cdc517e2706f1a3795b1\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:24:13-63
163            android:exported="true" />
163-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44341f99e7e4cdc517e2706f1a3795b1\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:25:13-36
164        <activity
164-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6187baae5010bfdf964065dbeecfd9\transformed\play-services-auth-21.1.1\AndroidManifest.xml:23:9-27:75
165            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
165-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6187baae5010bfdf964065dbeecfd9\transformed\play-services-auth-21.1.1\AndroidManifest.xml:24:13-93
166            android:excludeFromRecents="true"
166-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6187baae5010bfdf964065dbeecfd9\transformed\play-services-auth-21.1.1\AndroidManifest.xml:25:13-46
167            android:exported="false"
167-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6187baae5010bfdf964065dbeecfd9\transformed\play-services-auth-21.1.1\AndroidManifest.xml:26:13-37
168            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
168-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6187baae5010bfdf964065dbeecfd9\transformed\play-services-auth-21.1.1\AndroidManifest.xml:27:13-72
169        <!--
170            Service handling Google Sign-In user revocation. For apps that do not integrate with
171            Google Sign-In, this service will never be started.
172        -->
173        <service
173-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6187baae5010bfdf964065dbeecfd9\transformed\play-services-auth-21.1.1\AndroidManifest.xml:33:9-37:51
174            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
174-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6187baae5010bfdf964065dbeecfd9\transformed\play-services-auth-21.1.1\AndroidManifest.xml:34:13-89
175            android:exported="true"
175-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6187baae5010bfdf964065dbeecfd9\transformed\play-services-auth-21.1.1\AndroidManifest.xml:35:13-36
176            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
176-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6187baae5010bfdf964065dbeecfd9\transformed\play-services-auth-21.1.1\AndroidManifest.xml:36:13-107
177            android:visibleToInstantApps="true" />
177-->[com.google.android.gms:play-services-auth:21.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6187baae5010bfdf964065dbeecfd9\transformed\play-services-auth-21.1.1\AndroidManifest.xml:37:13-48
178
179        <property
179-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:30:9-32:61
180            android:name="android.adservices.AD_SERVICES_CONFIG"
180-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:31:13-65
181            android:resource="@xml/ga_ad_services_config" />
181-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a49a18f5a5e56b4ae8b59f78812f5c5\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:32:13-58
182
183        <provider
183-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
184            android:name="com.google.firebase.provider.FirebaseInitProvider"
184-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
185            android:authorities="com.Hamode.periodpal.firebaseinitprovider"
185-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
186            android:directBootAware="true"
186-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
187            android:exported="false"
187-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
188            android:initOrder="100" />
188-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa1ea386aa7fabbac5e69176683fd0f4\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
189
190        <receiver
190-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:29:9-33:20
191            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
191-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:30:13-85
192            android:enabled="true"
192-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:31:13-35
193            android:exported="false" >
193-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:32:13-37
194        </receiver>
195
196        <service
196-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:35:9-38:40
197            android:name="com.google.android.gms.measurement.AppMeasurementService"
197-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:36:13-84
198            android:enabled="true"
198-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:37:13-35
199            android:exported="false" />
199-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:38:13-37
200        <service
200-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:39:9-43:72
201            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
201-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:40:13-87
202            android:enabled="true"
202-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:41:13-35
203            android:exported="false"
203-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:42:13-37
204            android:permission="android.permission.BIND_JOB_SERVICE" />
204-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4cd372420b4e1735990804ef8cf4a47\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:43:13-69
205
206        <provider
206-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97780ad0920f6e19384830ccc4c0c93\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
207            android:name="androidx.startup.InitializationProvider"
207-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97780ad0920f6e19384830ccc4c0c93\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
208            android:authorities="com.Hamode.periodpal.androidx-startup"
208-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97780ad0920f6e19384830ccc4c0c93\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
209            android:exported="false" >
209-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97780ad0920f6e19384830ccc4c0c93\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
210            <meta-data
210-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97780ad0920f6e19384830ccc4c0c93\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
211                android:name="androidx.emoji2.text.EmojiCompatInitializer"
211-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97780ad0920f6e19384830ccc4c0c93\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
212                android:value="androidx.startup" />
212-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f97780ad0920f6e19384830ccc4c0c93\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
213            <meta-data
213-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b2d459fd073c606760ca12397d19b43\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
214                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
214-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b2d459fd073c606760ca12397d19b43\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
215                android:value="androidx.startup" />
215-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b2d459fd073c606760ca12397d19b43\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
216            <meta-data
216-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
217                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
217-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
218                android:value="androidx.startup" />
218-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
219        </provider>
220
221        <uses-library
221-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83c6958f34eec6cf068a7feaabaafa28\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
222            android:name="android.ext.adservices"
222-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83c6958f34eec6cf068a7feaabaafa28\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
223            android:required="false" />
223-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83c6958f34eec6cf068a7feaabaafa28\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
224
225        <activity
225-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cef95d772ca94c574c1d7981123fcc6\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
226            android:name="com.google.android.gms.common.api.GoogleApiActivity"
226-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cef95d772ca94c574c1d7981123fcc6\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
227            android:exported="false"
227-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cef95d772ca94c574c1d7981123fcc6\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
228            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
228-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cef95d772ca94c574c1d7981123fcc6\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
229
230        <meta-data
230-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b3163db060619b51a72cfb285e5abcba\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
231            android:name="com.google.android.gms.version"
231-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b3163db060619b51a72cfb285e5abcba\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
232            android:value="@integer/google_play_services_version" />
232-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b3163db060619b51a72cfb285e5abcba\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
233
234        <receiver
234-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
235            android:name="androidx.profileinstaller.ProfileInstallReceiver"
235-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
236            android:directBootAware="false"
236-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
237            android:enabled="true"
237-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
238            android:exported="true"
238-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
239            android:permission="android.permission.DUMP" >
239-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
240            <intent-filter>
240-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
241                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
241-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
241-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
242            </intent-filter>
243            <intent-filter>
243-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
244                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
244-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
244-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
245            </intent-filter>
246            <intent-filter>
246-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
247                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
247-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
247-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
248            </intent-filter>
249            <intent-filter>
249-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
250                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
250-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
250-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef0cc0f532e702c5a08afadf42c8a8b0\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
251            </intent-filter>
252        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
253        <activity
253-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bafe67932525e9d41a7253ce026a7fa1\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
254            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
254-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bafe67932525e9d41a7253ce026a7fa1\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
255            android:exported="false"
255-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bafe67932525e9d41a7253ce026a7fa1\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
256            android:stateNotNeeded="true"
256-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bafe67932525e9d41a7253ce026a7fa1\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
257            android:theme="@style/Theme.PlayCore.Transparent" />
257-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bafe67932525e9d41a7253ce026a7fa1\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
258    </application>
259
260</manifest>
