{"logs": [{"outputFile": "com.Hamode.periodpal.app-mergeDebugResources-61:/values-te/values-te.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4a4406fb22280fb302c67f992ef8133c\\transformed\\material3-release\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,184,315,429,559,663,762,878,1019,1131,1274,1358,1461,1557,1655,1771,1901,2009,2158,2305,2438,2634,2762,2878,2999,3136,3233,3330,3455,3583,3689,3795,3901,4044,4194,4302,4406,4482,4581,4682,4798,4892,4984,5091,5171,5254,5355,5483,5577,5689,5777,5888,5990,6107,6230,6310,6417", "endColumns": "128,130,113,129,103,98,115,140,111,142,83,102,95,97,115,129,107,148,146,132,195,127,115,120,136,96,96,124,127,105,105,105,142,149,107,103,75,98,100,115,93,91,106,79,82,100,127,93,111,87,110,101,116,122,79,106,96", "endOffsets": "179,310,424,554,658,757,873,1014,1126,1269,1353,1456,1552,1650,1766,1896,2004,2153,2300,2433,2629,2757,2873,2994,3131,3228,3325,3450,3578,3684,3790,3896,4039,4189,4297,4401,4477,4576,4677,4793,4887,4979,5086,5166,5249,5350,5478,5572,5684,5772,5883,5985,6102,6225,6305,6412,6509"}, "to": {"startLines": "40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4370,4499,4630,4744,4874,4978,5077,5193,5334,5446,5589,5673,5776,5872,5970,6086,6216,6324,6473,6620,6753,6949,7077,7193,7314,7451,7548,7645,7770,7898,8004,8110,8216,8359,8509,8617,8721,8797,8896,8997,9113,9207,9299,9406,9486,9569,9670,9798,9892,10004,10092,10203,10305,10422,10545,10625,10732", "endColumns": "128,130,113,129,103,98,115,140,111,142,83,102,95,97,115,129,107,148,146,132,195,127,115,120,136,96,96,124,127,105,105,105,142,149,107,103,75,98,100,115,93,91,106,79,82,100,127,93,111,87,110,101,116,122,79,106,96", "endOffsets": "4494,4625,4739,4869,4973,5072,5188,5329,5441,5584,5668,5771,5867,5965,6081,6211,6319,6468,6615,6748,6944,7072,7188,7309,7446,7543,7640,7765,7893,7999,8105,8211,8354,8504,8612,8716,8792,8891,8992,9108,9202,9294,9401,9481,9564,9665,9793,9887,9999,10087,10198,10300,10417,10540,10620,10727,10824"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e9aad415eee13c5225ceb41d0c3631e4\\transformed\\foundation-release\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,94", "endOffsets": "138,233"}, "to": {"startLines": "109,110", "startColumns": "4,4", "startOffsets": "11857,11945", "endColumns": "87,94", "endOffsets": "11940,12035"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\11cdba141bdfb547960b7fcdc0b4367a\\transformed\\ui-release\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,292,389,489,578,667,763,851,935,1019,1109,1186,1268,1348,1427,1504,1573", "endColumns": "97,88,96,99,88,88,95,87,83,83,89,76,81,79,78,76,68,116", "endOffsets": "198,287,384,484,573,662,758,846,930,1014,1104,1181,1263,1343,1422,1499,1568,1685"}, "to": {"startLines": "11,12,32,33,34,38,39,97,98,99,100,101,102,103,104,106,107,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1083,1181,3575,3672,3772,4185,4274,10829,10917,11001,11085,11175,11252,11334,11414,11594,11671,11740", "endColumns": "97,88,96,99,88,88,95,87,83,83,89,76,81,79,78,76,68,116", "endOffsets": "1176,1265,3667,3767,3856,4269,4365,10912,10996,11080,11170,11247,11329,11409,11488,11666,11735,11852"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9f3b0e192fae8d81415bbd16b5199dc2\\transformed\\browser-1.4.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,272,383", "endColumns": "110,105,110,106", "endOffsets": "161,267,378,485"}, "to": {"startLines": "31,35,36,37", "startColumns": "4,4,4,4", "startOffsets": "3464,3861,3967,4078", "endColumns": "110,105,110,106", "endOffsets": "3570,3962,4073,4180"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c2e988808bb7f73a8315e368f7c5f6e3\\transformed\\core-1.13.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,265,367,468,574,681,805", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "152,260,362,463,569,676,800,901"}, "to": {"startLines": "4,5,6,7,8,9,10,105", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "333,435,543,645,746,852,959,11493", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "430,538,640,741,847,954,1078,11589"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e8b5bc1662136e087738bd938abfe3e7\\transformed\\credentials-1.3.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,169", "endColumns": "113,113", "endOffsets": "164,278"}, "to": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "105,219", "endColumns": "113,113", "endOffsets": "214,328"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9cef95d772ca94c574c1d7981123fcc6\\transformed\\play-services-base-18.5.0\\res\\values-te\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,301,451,577,688,821,942,1043,1139,1284,1392,1541,1669,1816,1975,2035,2101", "endColumns": "107,149,125,110,132,120,100,95,144,107,148,127,146,158,59,65,79", "endOffsets": "300,450,576,687,820,941,1042,1138,1283,1391,1540,1668,1815,1974,2034,2100,2180"}, "to": {"startLines": "13,14,15,16,17,18,19,20,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1270,1382,1536,1666,1781,1918,2043,2148,2386,2535,2647,2800,2932,3083,3246,3310,3380", "endColumns": "111,153,129,114,136,124,104,99,148,111,152,131,150,162,63,69,83", "endOffsets": "1377,1531,1661,1776,1913,2038,2143,2243,2530,2642,2795,2927,3078,3241,3305,3375,3459"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b3163db060619b51a72cfb285e5abcba\\transformed\\play-services-basement-18.4.0\\res\\values-te\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "133", "endOffsets": "328"}, "to": {"startLines": "21", "startColumns": "4", "startOffsets": "2248", "endColumns": "137", "endOffsets": "2381"}}]}]}