package com.Hamode.periodpal.data.models

import java.time.LocalDate

/**
 * User profile data
 */
data class UserProfile(
    val userId: String = "",
    val email: String = "",
    val displayName: String = "",
    val dateOfBirth: LocalDate? = null,
    val averageCycleLength: Int = 28,
    val averagePeriodLength: Int = 5,
    val lastPeriodStart: LocalDate? = null,
    val notifications: NotificationSettings = NotificationSettings(),
    val privacy: PrivacySettings = PrivacySettings(),
    val createdAt: LocalDate = LocalDate.now(),
    val updatedAt: LocalDate = LocalDate.now()
) {
    fun toMap(): Map<String, Any> {
        return mapOf(
            "userId" to userId,
            "email" to email,
            "displayName" to displayName,
            "dateOfBirth" to (dateOfBirth?.toString() ?: ""),
            "averageCycleLength" to averageCycleLength,
            "averagePeriodLength" to averagePeriodLength,
            "lastPeriodStart" to (lastPeriodStart?.toString() ?: ""),
            "notifications" to notifications.toMap(),
            "privacy" to privacy.toMap(),
            "createdAt" to createdAt.toString(),
            "updatedAt" to updatedAt.toString()
        )
    }
    
    companion object {
        fun fromMap(map: Map<String, Any>): UserProfile {
            return UserProfile(
                userId = map["userId"] as? String ?: "",
                email = map["email"] as? String ?: "",
                displayName = map["displayName"] as? String ?: "",
                dateOfBirth = (map["dateOfBirth"] as? String)?.let { 
                    if (it.isNotEmpty()) LocalDate.parse(it) else null 
                },
                averageCycleLength = (map["averageCycleLength"] as? Long)?.toInt() ?: 28,
                averagePeriodLength = (map["averagePeriodLength"] as? Long)?.toInt() ?: 5,
                lastPeriodStart = (map["lastPeriodStart"] as? String)?.let { 
                    if (it.isNotEmpty()) LocalDate.parse(it) else null 
                },
                notifications = NotificationSettings.fromMap(map["notifications"] as? Map<String, Any> ?: emptyMap()),
                privacy = PrivacySettings.fromMap(map["privacy"] as? Map<String, Any> ?: emptyMap()),
                createdAt = LocalDate.parse(map["createdAt"] as? String ?: LocalDate.now().toString()),
                updatedAt = LocalDate.parse(map["updatedAt"] as? String ?: LocalDate.now().toString())
            )
        }
    }
}

/**
 * Notification settings
 */
data class NotificationSettings(
    val periodReminders: Boolean = true,
    val ovulationReminders: Boolean = true,
    val medicationReminders: Boolean = false,
    val dailyLogReminders: Boolean = true,
    val insightNotifications: Boolean = true,
    val reminderTime: String = "09:00" // HH:mm format
) {
    fun toMap(): Map<String, Any> {
        return mapOf(
            "periodReminders" to periodReminders,
            "ovulationReminders" to ovulationReminders,
            "medicationReminders" to medicationReminders,
            "dailyLogReminders" to dailyLogReminders,
            "insightNotifications" to insightNotifications,
            "reminderTime" to reminderTime
        )
    }
    
    companion object {
        fun fromMap(map: Map<String, Any>): NotificationSettings {
            return NotificationSettings(
                periodReminders = map["periodReminders"] as? Boolean ?: true,
                ovulationReminders = map["ovulationReminders"] as? Boolean ?: true,
                medicationReminders = map["medicationReminders"] as? Boolean ?: false,
                dailyLogReminders = map["dailyLogReminders"] as? Boolean ?: true,
                insightNotifications = map["insightNotifications"] as? Boolean ?: true,
                reminderTime = map["reminderTime"] as? String ?: "09:00"
            )
        }
    }
}

/**
 * Privacy settings
 */
data class PrivacySettings(
    val shareDataForResearch: Boolean = false,
    val allowAnalytics: Boolean = true,
    val biometricLock: Boolean = false,
    val dataRetentionMonths: Int = 24
) {
    fun toMap(): Map<String, Any> {
        return mapOf(
            "shareDataForResearch" to shareDataForResearch,
            "allowAnalytics" to allowAnalytics,
            "biometricLock" to biometricLock,
            "dataRetentionMonths" to dataRetentionMonths
        )
    }
    
    companion object {
        fun fromMap(map: Map<String, Any>): PrivacySettings {
            return PrivacySettings(
                shareDataForResearch = map["shareDataForResearch"] as? Boolean ?: false,
                allowAnalytics = map["allowAnalytics"] as? Boolean ?: true,
                biometricLock = map["biometricLock"] as? Boolean ?: false,
                dataRetentionMonths = (map["dataRetentionMonths"] as? Long)?.toInt() ?: 24
            )
        }
    }
}

// Firebase serialization extensions for existing models
fun DailyLog.toMap(): Map<String, Any> {
    return mapOf(
        "dateString" to date.toString(),
        "flowIntensity" to flowIntensity.name,
        "painLevel" to painLevel.name,
        "mood" to (mood?.name ?: ""),
        "symptoms" to symptoms.map { it.name },
        "notes" to notes,
        "temperature" to (temperature ?: 0.0),
        "weight" to (weight ?: 0.0),
        "sleepHours" to (sleepHours ?: 0.0),
        "exerciseMinutes" to (exerciseMinutes ?: 0),
        "waterGlasses" to (waterGlasses ?: 0)
    )
}

fun dailyLogFromMap(map: Map<String, Any>): DailyLog {
    return DailyLog(
        date = LocalDate.parse(map["dateString"] as String),
        flowIntensity = FlowIntensity.valueOf(map["flowIntensity"] as? String ?: "NONE"),
        painLevel = PainLevel.valueOf(map["painLevel"] as? String ?: "NONE"),
        mood = (map["mood"] as? String)?.let { if (it.isNotEmpty()) MoodState.valueOf(it) else null },
        symptoms = (map["symptoms"] as? List<String>)?.mapNotNull { 
            try { Symptom.valueOf(it) } catch (e: Exception) { null }
        } ?: emptyList(),
        notes = map["notes"] as? String ?: "",
        temperature = (map["temperature"] as? Number)?.toDouble(),
        weight = (map["weight"] as? Number)?.toDouble(),
        sleepHours = (map["sleepHours"] as? Number)?.toDouble(),
        exerciseMinutes = (map["exerciseMinutes"] as? Number)?.toInt(),
        waterGlasses = (map["waterGlasses"] as? Number)?.toInt()
    )
}

fun HealthInsight.toMap(): Map<String, Any> {
    return mapOf(
        "id" to id,
        "type" to type.name,
        "severity" to severity.name,
        "title" to title,
        "description" to description,
        "recommendation" to recommendation,
        "date" to date.toString(),
        "isRead" to isRead
    )
}

fun healthInsightFromMap(map: Map<String, Any>): HealthInsight {
    return HealthInsight(
        id = map["id"] as String,
        type = HealthInsightType.valueOf(map["type"] as String),
        severity = HealthInsightSeverity.valueOf(map["severity"] as String),
        title = map["title"] as String,
        description = map["description"] as String,
        recommendation = map["recommendation"] as String,
        date = LocalDate.parse(map["date"] as String),
        isRead = map["isRead"] as? Boolean ?: false
    )
}
