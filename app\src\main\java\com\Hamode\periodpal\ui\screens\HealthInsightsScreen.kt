package com.Hamode.periodpal.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.Hamode.periodpal.data.models.*
import com.Hamode.periodpal.ui.theme.*
import java.time.LocalDate
import java.time.format.DateTimeFormatter

@Composable
fun HealthInsightsScreen(
    modifier: Modifier = Modifier
) {
    var selectedTab by remember { mutableStateOf(0) }
    val tabs = listOf("Overview", "Patterns", "Predictions")

    Column(
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background)
            .padding(16.dp)
    ) {
        // Header
        InsightsHeader(
            modifier = Modifier.fillMaxWidth()
        )

        Spacer(modifier = Modifier.height(16.dp))

        // Tab Row
        TabRow(
            selectedTabIndex = selectedTab,
            containerColor = MaterialTheme.colorScheme.surface,
            contentColor = RosePink40,
            modifier = Modifier.fillMaxWidth()
        ) {
            tabs.forEachIndexed { index, title ->
                Tab(
                    selected = selectedTab == index,
                    onClick = { selectedTab = index },
                    text = {
                        Text(
                            text = title,
                            fontWeight = if (selectedTab == index) FontWeight.Bold else FontWeight.Normal
                        )
                    }
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Tab Content
        when (selectedTab) {
            0 -> OverviewTab()
            1 -> PatternsTab()
            2 -> PredictionsTab()
        }
    }
}

@Composable
private fun InsightsHeader(
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = RosePink40
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.Analytics,
                contentDescription = "Health Insights",
                tint = Color.White,
                modifier = Modifier.size(32.dp)
            )
            Spacer(modifier = Modifier.width(16.dp))
            Column {
                Text(
                    text = "Health Insights",
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
                Text(
                    text = "Discover patterns in your cycle",
                    fontSize = 14.sp,
                    color = Color.White.copy(alpha = 0.9f)
                )
            }
        }
    }
}

@Composable
private fun OverviewTab() {
    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(16.dp),
        modifier = Modifier.fillMaxSize()
    ) {
        // Cycle Regularity Card
        item {
            InsightCard(
                title = "Cycle Regularity",
                icon = Icons.Default.TrendingUp,
                iconColor = SuccessGreen,
                content = {
                    Column {
                        Text(
                            text = "Very Regular",
                            fontSize = 18.sp,
                            fontWeight = FontWeight.Bold,
                            color = SuccessGreen
                        )
                        Text(
                            text = "Your cycles are consistently 28 days with minimal variation. This indicates excellent hormonal balance.",
                            fontSize = 14.sp,
                            color = DarkGray,
                            modifier = Modifier.padding(top = 4.dp)
                        )
                    }
                }
            )
        }

        // Recent Insights
        item {
            InsightCard(
                title = "Recent Insights",
                icon = Icons.Default.Lightbulb,
                iconColor = WarningOrange,
                content = {
                    Column(
                        verticalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        InsightItem(
                            text = "Your period length has been consistent at 5 days for the past 3 cycles.",
                            type = HealthInsightSeverity.LOW
                        )
                        InsightItem(
                            text = "You tend to experience more symptoms during the luteal phase.",
                            type = HealthInsightSeverity.MEDIUM
                        )
                        InsightItem(
                            text = "Consider tracking sleep patterns - they may correlate with your mood changes.",
                            type = HealthInsightSeverity.INFO
                        )
                    }
                }
            )
        }

        // Health Score
        item {
            InsightCard(
                title = "Health Score",
                icon = Icons.Default.Favorite,
                iconColor = RosePink40,
                content = {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Column(modifier = Modifier.weight(1f)) {
                            Text(
                                text = "85/100",
                                fontSize = 32.sp,
                                fontWeight = FontWeight.Bold,
                                color = RosePink40
                            )
                            Text(
                                text = "Excellent reproductive health",
                                fontSize = 14.sp,
                                color = DarkGray
                            )
                        }
                        CircularProgressIndicator(
                            progress = 0.85f,
                            modifier = Modifier.size(60.dp),
                            color = RosePink40,
                            strokeWidth = 6.dp
                        )
                    }
                }
            )
        }

        // Bottom spacing
        item {
            Spacer(modifier = Modifier.height(80.dp))
        }
    }
}

@Composable
private fun PatternsTab() {
    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(16.dp),
        modifier = Modifier.fillMaxSize()
    ) {
        item {
            InsightCard(
                title = "Symptom Patterns",
                icon = Icons.Default.BarChart,
                iconColor = InfoBlue,
                content = {
                    Column(
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        SymptomPatternItem("Cramps", "Days 1-3", 85)
                        SymptomPatternItem("Mood Changes", "Days 24-28", 70)
                        SymptomPatternItem("Bloating", "Days 26-2", 60)
                        SymptomPatternItem("Headaches", "Days 1-2", 45)
                    }
                }
            )
        }

        item {
            InsightCard(
                title = "Flow Patterns",
                icon = Icons.Default.WaterDrop,
                iconColor = PeriodRed,
                content = {
                    Text(
                        text = "Your flow typically follows a consistent pattern: Heavy on days 1-2, medium on day 3, and light on days 4-5.",
                        fontSize = 14.sp,
                        color = DarkGray
                    )
                }
            )
        }

        // Bottom spacing
        item {
            Spacer(modifier = Modifier.height(80.dp))
        }
    }
}

@Composable
private fun PredictionsTab() {
    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(16.dp),
        modifier = Modifier.fillMaxSize()
    ) {
        item {
            InsightCard(
                title = "Next Period Prediction",
                icon = Icons.Default.CalendarToday,
                iconColor = PeriodRed,
                content = {
                    Column {
                        Text(
                            text = "Expected: ${LocalDate.now().plusDays(12).format(DateTimeFormatter.ofPattern("MMM dd, yyyy"))}",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold,
                            color = PeriodRed
                        )
                        Text(
                            text = "Confidence: 95% (based on 6 months of data)",
                            fontSize = 12.sp,
                            color = DarkGray,
                            modifier = Modifier.padding(top = 4.dp)
                        )
                    }
                }
            )
        }

        item {
            InsightCard(
                title = "Fertile Window",
                icon = Icons.Default.Eco,
                iconColor = FertileGreen,
                content = {
                    Column {
                        Text(
                            text = "Next fertile window: ${LocalDate.now().minusDays(2).format(DateTimeFormatter.ofPattern("MMM dd"))} - ${LocalDate.now().plusDays(2).format(DateTimeFormatter.ofPattern("MMM dd"))}",
                            fontSize = 14.sp,
                            color = DarkGray
                        )
                        Text(
                            text = "Peak fertility: ${LocalDate.now().format(DateTimeFormatter.ofPattern("MMM dd"))}",
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Bold,
                            color = FertileGreen,
                            modifier = Modifier.padding(top = 4.dp)
                        )
                    }
                }
            )
        }

        // Bottom spacing
        item {
            Spacer(modifier = Modifier.height(80.dp))
        }
    }
}

@Composable
private fun InsightCard(
    title: String,
    icon: ImageVector,
    iconColor: Color,
    content: @Composable () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(bottom = 12.dp)
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = title,
                    tint = iconColor,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.onSurface
                )
            }
            content()
        }
    }
}

@Composable
private fun InsightItem(
    text: String,
    type: HealthInsightSeverity,
    modifier: Modifier = Modifier
) {
    val color = when (type) {
        HealthInsightSeverity.INFO -> InfoBlue
        HealthInsightSeverity.LOW -> SuccessGreen
        HealthInsightSeverity.MEDIUM -> WarningOrange
        HealthInsightSeverity.HIGH -> ErrorRed
    }

    Row(
        modifier = modifier,
        verticalAlignment = Alignment.Top
    ) {
        Box(
            modifier = Modifier
                .size(8.dp)
                .background(color, RoundedCornerShape(4.dp))
                .padding(top = 6.dp)
        )
        Spacer(modifier = Modifier.width(8.dp))
        Text(
            text = text,
            fontSize = 14.sp,
            color = DarkGray,
            modifier = Modifier.weight(1f)
        )
    }
}

@Composable
private fun SymptomPatternItem(
    symptom: String,
    timing: String,
    percentage: Int,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = symptom,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onSurface
            )
            Text(
                text = "$percentage%",
                fontSize = 12.sp,
                color = DarkGray
            )
        }
        Text(
            text = timing,
            fontSize = 12.sp,
            color = DarkGray
        )
        LinearProgressIndicator(
            progress = percentage / 100f,
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 4.dp),
            color = RosePink40,
            trackColor = LightGray
        )
    }
}
