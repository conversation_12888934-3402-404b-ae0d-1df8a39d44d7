package com.Hamode.periodpal.ui.navigation

/**
 * Main navigation destinations
 */
enum class NavigationDestination(val displayName: String) {
    HOME("Home"),
    CALENDAR("Calendar"),
    HEALTH_LOGS("Health Logs"),
    INSIGHTS("Insights"),
    PROFILE("Profile")
}

/**
 * Secondary navigation destinations (overlays/modals)
 */
enum class SecondaryDestination(val displayName: String) {
    SYMPTOM_LOGGING("Log Symptoms"),
    CYCLE_HISTORY("Cycle History"),
    SETTINGS("Settings"),
    EMERGENCY_CONTACTS("Emergency Contacts"),
    EXPORT_DATA("Export Data"),
    ABOUT("About"),
    HELP("Help")
}
